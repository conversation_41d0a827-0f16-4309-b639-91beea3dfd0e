{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cuQA96FcoraD", "outputId": "4996fc4e-172d-4e61-ef38-ec4e9adb39ae"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["2025-06-02 12:14:33,040 - INFO - 训练配置: {'batch_size': 16, 'epochs': 20, 'lr': 0.0001, 'd_lr': 0.0002, 'weight_decay': 1e-06, 'scheduler': 'plateau', 'lr_patience': 3, 'lr_factor': 0.5, 'min_lr': 1e-07, 'MS_data_dir': '/content/drive/MyDrive/d2l-zh/SRGAN/data/train/MS', 'NTL_data_dir': '/content/drive/MyDrive/d2l-zh/SRGAN/data/train/NTL', 'save_dir': '/content/drive/MyDrive/d2l-zh/SRGAN/data/model', 'up_scale': 4, 'cuda': True, 'lambda_rad': 1, 'lambda_perc': 2, 'lambda_struct': 1, 'lambda_adv': 1, 'lambda_cycle': 1, 'lambda_identity': 0, 'save_interval': 10, 'val_ratio': 0.1, 'seed': 42, 'n_critic': 1, 'warmup_epochs': 2}\n", "2025-06-02 12:14:33,040 - INFO - 使用设备: cuda\n", "2025-06-02 12:14:33,434 - INFO - G_LR_to_HR参数数量: 1039539\n", "2025-06-02 12:14:33,435 - INFO - G_HR_to_LR参数数量: 763764\n", "2025-06-02 12:14:33,435 - INFO - D_HR参数数量: 1555329\n", "2025-06-02 12:14:33,436 - INFO - D_LR参数数量: 1555329\n", "/usr/local/lib/python3.11/dist-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.\n", "  warnings.warn(\n", "2025-06-02 12:14:33,437 - INFO - 使用ReduceLROnPlateau学习率调度器 (因子: 0.5, 耐心值: 3)\n", "/usr/local/lib/python3.11/dist-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.\n", "  warnings.warn(\n", "/usr/local/lib/python3.11/dist-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=VGG19_Weights.IMAGENET1K_V1`. You can also use `weights=VGG19_Weights.DEFAULT` to get the most up-to-date weights.\n", "  warnings.warn(msg)\n", "No blur will be applied to NTL HR before downsampling.\n", "2025-06-02 12:14:36,950 - INFO - 数据集总大小: 1600\n", "2025-06-02 12:14:36,952 - INFO - 训练集大小: 1440\n", "2025-06-02 12:14:36,952 - INFO - 验证集大小: 160\n", "/usr/local/lib/python3.11/dist-packages/torch/utils/data/dataloader.py:624: UserWarning: This DataLoader will create 4 worker processes in total. Our suggested max number of worker in current system is 2, which is smaller than what this DataLoader is going to create. Please be aware that excessive worker creation might get DataLoader running slow or even freeze, lower the worker number to avoid potential slowness/freeze if necessary.\n", "  warnings.warn(\n", "2025-06-02 12:15:08,265 - INFO - Warmup Epoch [1/20], Step [10/90], G Loss: 0.0698,\n", "2025-06-02 12:15:15,907 - INFO - Warmup Epoch [1/20], Step [20/90], G Loss: 0.0325,\n", "2025-06-02 12:15:28,372 - INFO - Warmup Epoch [1/20], Step [30/90], G Loss: 0.0729,\n", "2025-06-02 12:15:37,871 - INFO - Warmup Epoch [1/20], Step [40/90], G Loss: 0.0273,\n", "2025-06-02 12:15:50,524 - INFO - Warmup Epoch [1/20], Step [50/90], G Loss: 0.0240,\n", "2025-06-02 12:15:57,884 - INFO - Warmup Epoch [1/20], Step [60/90], G Loss: 0.0216,\n", "2025-06-02 12:16:10,016 - INFO - Warmup Epoch [1/20], Step [70/90], G Loss: 0.0221,\n", "2025-06-02 12:16:19,295 - INFO - Warmup Epoch [1/20], <PERSON> [80/90], G Loss: 0.0220,\n", "2025-06-02 12:16:28,206 - INFO - Warmup Epoch [1/20], Step [90/90], G Loss: 0.0143,\n", "2025-06-02 12:16:46,879 - INFO - Epoch [1/20], G训练: 0.0541, G验证: 0.0260, D_HR训练: 0.0000, D_HR验证: 0.0000, D_LR训练: 0.0000, D_LR验证: 0.0000, 循环损失: 0.0260,  PSNR: 57.11dB, SSIM: 0.9921,mixed_score：0.9965 学习率G: 1.00e-04, 学习率D_HR: 2.00e-04, 学习率D_LR: 2.00e-04, 时间: 0:02:09.917900\n", "2025-06-02 12:16:47,059 - INFO - 保存最佳模型到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth\n", "2025-06-02 12:16:59,904 - INFO - Warmup Epoch [2/20], Step [10/90], G Loss: 0.0455,\n", "2025-06-02 12:17:17,236 - INFO - <PERSON><PERSON> Epoch [2/20], <PERSON> [20/90], G Loss: 0.0435,\n", "2025-06-02 12:17:29,738 - INFO - <PERSON>up Epoch [2/20], Step [30/90], G Loss: 0.0465,\n", "2025-06-02 12:17:40,208 - INFO - Warmup Epoch [2/20], Step [40/90], G Loss: 0.0094,\n", "2025-06-02 12:17:51,139 - INFO - <PERSON>up Epoch [2/20], <PERSON> [50/90], G Loss: 0.0226,\n", "2025-06-02 12:17:59,188 - INFO - <PERSON>up Epoch [2/20], <PERSON> [60/90], G Loss: 0.0072,\n", "2025-06-02 12:18:11,373 - INFO - Warmup Epoch [2/20], Step [70/90], G Loss: 0.0179,\n", "2025-06-02 12:18:21,235 - INFO - Warmup Epoch [2/20], <PERSON> [80/90], G Loss: 0.0095,\n", "2025-06-02 12:18:29,486 - INFO - <PERSON>up Epoch [2/20], Step [90/90], G Loss: 0.0369,\n", "2025-06-02 12:18:40,525 - INFO - Epoch [2/20], G训练: 0.0277, G验证: 0.0187, D_HR训练: 0.0000, D_HR验证: 0.0000, D_LR训练: 0.0000, D_LR验证: 0.0000, 循环损失: 0.0187,  PSNR: 57.95dB, SSIM: 0.9874,mixed_score：0.9943 学习率G: 1.00e-04, 学习率D_HR: 2.00e-04, 学习率D_LR: 2.00e-04, 时间: 0:01:53.463479\n", "2025-06-02 12:19:02,951 - INFO - Epoch [3/20], Step [10/90], G Loss: 0.9838, D_HR Loss: 0.8774, D_LR Loss: 0.9000,Adv: 0.9732, Cycle: 0.0106 \n", "2025-06-02 12:19:21,063 - INFO - Epoch [3/20], Step [20/90], G Loss: 1.3890, D_HR Loss: 1.0010, D_LR Loss: 0.7303,Adv: 1.3689, Cycle: 0.0201 \n", "2025-06-02 12:19:33,198 - INFO - Epoch [3/20], Step [30/90], G Loss: 1.4775, D_HR Loss: 0.7338, D_LR Loss: 0.7182,Adv: 1.4546, Cycle: 0.0229 \n", "2025-06-02 12:19:43,206 - INFO - Epoch [3/20], Step [40/90], G Loss: 1.8329, D_HR Loss: 0.7528, D_LR Loss: 0.7175,Adv: 1.8200, Cycle: 0.0129 \n", "2025-06-02 12:19:56,053 - INFO - Epoch [3/20], Step [50/90], G Loss: 1.6931, D_HR Loss: 0.6936, D_LR Loss: 0.7361,Adv: 1.6551, Cycle: 0.0380 \n", "2025-06-02 12:20:04,098 - INFO - Epoch [3/20], Step [60/90], G Loss: 1.4251, D_HR Loss: 0.6923, D_LR Loss: 0.6937,Adv: 1.4049, Cycle: 0.0202 \n", "2025-06-02 12:20:17,275 - INFO - Epoch [3/20], Step [70/90], G Loss: 1.2526, D_HR Loss: 0.6874, D_LR Loss: 0.7047,Adv: 1.2370, Cycle: 0.0156 \n", "2025-06-02 12:20:26,913 - INFO - Epoch [3/20], Step [80/90], G Loss: 1.6560, D_HR Loss: 0.6842, D_LR Loss: 0.7009,Adv: 1.5196, Cycle: 0.1364 \n", "2025-06-02 12:20:36,730 - INFO - Epoch [3/20], Step [90/90], G Loss: 1.8681, D_HR Loss: 0.6947, D_LR Loss: 0.6963,Adv: 1.8312, Cycle: 0.0368 \n", "2025-06-02 12:20:52,803 - INFO - Epoch [3/20], G训练: 1.5717, G验证: 1.8661, D_HR训练: 0.7656, D_HR验证: 0.6874, D_LR训练: 0.7337, D_LR验证: 0.7432, 循环损失: 0.0330,  PSNR: 57.15dB, SSIM: 0.9929,mixed_score：0.9968 学习率G: 1.00e-04, 学习率D_HR: 2.00e-04, 学习率D_LR: 2.00e-04, 时间: 0:02:12.275578\n", "2025-06-02 12:20:53,055 - INFO - 保存最佳模型到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth\n", "2025-06-02 12:21:07,521 - INFO - Epoch [4/20], Step [10/90], G Loss: 0.7700, D_HR Loss: 0.6684, D_LR Loss: 0.6975,Adv: 0.7579, Cycle: 0.0120 \n", "2025-06-02 12:21:23,511 - INFO - Epoch [4/20], Step [20/90], G Loss: 1.3960, D_HR Loss: 0.7029, D_LR Loss: 0.6913,Adv: 1.3781, Cycle: 0.0179 \n"]}], "source": ["!python /content/drive/MyDrive/d2l-zh/SRGAN/train.py"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 6452, "status": "ok", "timestamp": 1748864995436, "user": {"displayName": "D J", "userId": "18240670107069857561"}, "user_tz": -480}, "id": "3rPuSnZ6orQr", "outputId": "23a9fe0b-48ff-4c1d-ca9d-00b1c5b6cc08"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Namespace(MS_data_dir='/content/drive/MyDrive/d2l-zh/SRGAN/data/predit/MS', NPP_data_dir='/content/drive/MyDrive/d2l-zh/SRGAN/data/predit/NPP', model='/content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth', save_folder='/content/drive/MyDrive/d2l-zh/SRGAN/data/out', cuda=True)\n", "使用设备: cuda\n", "成功加载 CycleGAN 模型的 G_LR_to_HR 生成器，并切换到 eval 模式\n", "找到 5 个多光谱图像\n", "找到 5 个NPP图像\n", "处理的图像对数量: 5\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_1.tif\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_2.tif\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_3.tif\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_4.tif\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_5.tif\n", "收皮\n"]}], "source": ["!python /content/drive/MyDrive/d2l-zh/SRGAN/test.py"]}, {"cell_type": "code", "source": [], "metadata": {"id": "rlAcUai3wR6c"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 8781, "status": "ok", "timestamp": 1748851211619, "user": {"displayName": "D J", "userId": "18240670107069857561"}, "user_tz": -480}, "id": "hVZEBNhworA8", "outputId": "18e2b984-ccc3-466a-f756-c3af0f31f463"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting rasterio\n", "  Downloading rasterio-1.4.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (9.1 kB)\n", "Collecting affine (from rasterio)\n", "  Downloading affine-2.4.0-py3-none-any.whl.metadata (4.0 kB)\n", "Requirement already satisfied: attrs in /usr/local/lib/python3.11/dist-packages (from rasterio) (25.3.0)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from rasterio) (2025.4.26)\n", "Requirement already satisfied: click>=4.0 in /usr/local/lib/python3.11/dist-packages (from rasterio) (8.2.1)\n", "Collecting cligj>=0.5 (from rasterio)\n", "  Downloading cligj-0.7.2-py3-none-any.whl.metadata (5.0 kB)\n", "Requirement already satisfied: numpy>=1.24 in /usr/local/lib/python3.11/dist-packages (from rasterio) (2.0.2)\n", "Collecting click-plugins (from rasterio)\n", "  Downloading click_plugins-1.1.1-py2.py3-none-any.whl.metadata (6.4 kB)\n", "Requirement already satisfied: pyparsing in /usr/local/lib/python3.11/dist-packages (from rasterio) (3.2.3)\n", "Downloading rasterio-1.4.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (22.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m22.2/22.2 MB\u001b[0m \u001b[31m51.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading cligj-0.7.2-py3-none-any.whl (7.1 kB)\n", "Downloading affine-2.4.0-py3-none-any.whl (15 kB)\n", "Downloading click_plugins-1.1.1-py2.py3-none-any.whl (7.5 kB)\n", "Installing collected packages: cligj, click-plugins, affine, rasterio\n", "Successfully installed affine-2.4.0 click-plugins-1.1.1 cligj-0.7.2 rasterio-1.4.3\n"]}], "source": ["!pip install rasterio"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 88729, "status": "ok", "timestamp": 1748851300486, "user": {"displayName": "D J", "userId": "18240670107069857561"}, "user_tz": -480}, "id": "4lEir2SAPO24", "outputId": "fbc489b7-3989-44ce-e7e0-bbed2c4e2fad"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting pytorch_msssim\n", "  Downloading pytorch_msssim-1.0.0-py3-none-any.whl.metadata (8.0 kB)\n", "Requirement already satisfied: torch in /usr/local/lib/python3.11/dist-packages (from pytorch_msssim) (2.6.0+cu124)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (3.18.0)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (4.13.2)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (3.4.2)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (3.1.6)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (2025.3.2)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.4.127 (from torch->pytorch_msssim)\n", "  Downloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-runtime-cu12==12.4.127 (from torch->pytorch_msssim)\n", "  Downloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.4.127 (from torch->pytorch_msssim)\n", "  Downloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cublas-cu12==******** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cufft-cu12==******** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-curand-cu12==********** (from torch->pytorch_msssim)\n", "  Downloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cusolver-cu12==******** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cusparse-cu12==********** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (0.6.2)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (12.4.127)\n", "Collecting nvidia-nvjitlink-cu12==12.4.127 (from torch->pytorch_msssim)\n", "  Downloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Requirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (3.2.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch->pytorch_msssim) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch->pytorch_msssim) (3.0.2)\n", "Downloading pytorch_msssim-1.0.0-py3-none-any.whl (7.7 kB)\n", "Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl (363.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m4.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (13.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m67.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (24.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.6/24.6 MB\u001b[0m \u001b[31m54.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (883 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m883.7/883.7 kB\u001b[0m \u001b[31m23.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl (664.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m2.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl (211.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m6.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl (56.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m13.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl (127.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m7.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl (207.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m5.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m41.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: nvidia-nvjitlink-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, pytorch_msssim\n", "  Attempting uninstall: nvidia-nvjitlink-cu12\n", "    Found existing installation: nvidia-nvjitlink-cu12 12.5.82\n", "    Uninstalling nvidia-nvjitlink-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-nvjitlink-cu12-12.5.82\n", "  Attempting uninstall: nvidia-curand-cu12\n", "    Found existing installation: nvidia-curand-cu12 10.3.6.82\n", "    Uninstalling nvidia-curand-cu12-10.3.6.82:\n", "      Successfully uninstalled nvidia-curand-cu12-10.3.6.82\n", "  Attempting uninstall: nvidia-cufft-cu12\n", "    Found existing installation: nvidia-cufft-cu12 11.2.3.61\n", "    Uninstalling nvidia-cufft-cu12-11.2.3.61:\n", "      Successfully uninstalled nvidia-cufft-cu12-11.2.3.61\n", "  Attempting uninstall: nvidia-cuda-runtime-cu12\n", "    Found existing installation: nvidia-cuda-runtime-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-runtime-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-runtime-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-nvrtc-cu12\n", "    Found existing installation: nvidia-cuda-nvrtc-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-nvrtc-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-nvrtc-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-cupti-cu12\n", "    Found existing installation: nvidia-cuda-cupti-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-cupti-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-cupti-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cublas-cu12\n", "    Found existing installation: nvidia-cublas-cu12 12.5.3.2\n", "    Uninstalling nvidia-cublas-cu12-12.5.3.2:\n", "      Successfully uninstalled nvidia-cublas-cu12-12.5.3.2\n", "  Attempting uninstall: nvidia-cusparse-cu12\n", "    Found existing installation: nvidia-cusparse-cu12 12.5.1.3\n", "    Uninstalling nvidia-cusparse-cu12-12.5.1.3:\n", "      Successfully uninstalled nvidia-cusparse-cu12-12.5.1.3\n", "  Attempting uninstall: nvidia-cudnn-cu12\n", "    Found existing installation: nvidia-cudnn-cu12 9.3.0.75\n", "    Uninstalling nvidia-cudnn-cu12-9.3.0.75:\n", "      Successfully uninstalled nvidia-cudnn-cu12-9.3.0.75\n", "  Attempting uninstall: nvidia-cusolver-cu12\n", "    Found existing installation: nvidia-cusolver-cu12 *********\n", "    Uninstalling nvidia-cusolver-cu12-*********:\n", "      Successfully uninstalled nvidia-cusolver-cu12-*********\n", "Successfully installed nvidia-cublas-cu12-******** nvidia-cuda-cupti-cu12-12.4.127 nvidia-cuda-nvrtc-cu12-12.4.127 nvidia-cuda-runtime-cu12-12.4.127 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-******** nvidia-curand-cu12-********** nvidia-cusolver-cu12-******** nvidia-cusparse-cu12-********** nvidia-nvjitlink-cu12-12.4.127 pytorch_msssim-1.0.0\n"]}], "source": ["!pip install pytorch_msssim"]}, {"cell_type": "code", "source": ["pip install torchmetrics"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "VwdaJNE2nctN", "executionInfo": {"status": "ok", "timestamp": 1748844157971, "user_tz": -480, "elapsed": 3648, "user": {"displayName": "XIAOCHUN", "userId": "00701490123473172790"}}, "outputId": "355f4b3d-03e7-46a5-db3c-989481aeb354"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting torchmetrics\n", "  Downloading torchmetrics-1.7.2-py3-none-any.whl.metadata (21 kB)\n", "Requirement already satisfied: numpy>1.20.0 in /usr/local/lib/python3.11/dist-packages (from torchmetrics) (2.0.2)\n", "Requirement already satisfied: packaging>17.1 in /usr/local/lib/python3.11/dist-packages (from torchmetrics) (24.2)\n", "Requirement already satisfied: torch>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from torchmetrics) (2.6.0+cu124)\n", "Collecting lightning-utilities>=0.8.0 (from torchmetrics)\n", "  Downloading lightning_utilities-0.14.3-py3-none-any.whl.metadata (5.6 kB)\n", "Requirement already satisfied: setuptools in /usr/local/lib/python3.11/dist-packages (from lightning-utilities>=0.8.0->torchmetrics) (75.2.0)\n", "Requirement already satisfied: typing_extensions in /usr/local/lib/python3.11/dist-packages (from lightning-utilities>=0.8.0->torchmetrics) (4.13.2)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (3.18.0)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (3.4.2)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (3.1.6)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (2025.3.2)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (12.4.127)\n", "Requirement already satisfied: nvidia-cudnn-cu12==******** in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (********)\n", "Requirement already satisfied: nvidia-cublas-cu12==******** in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (********)\n", "Requirement already satisfied: nvidia-cufft-cu12==******** in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (********)\n", "Requirement already satisfied: nvidia-curand-cu12==********** in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (**********)\n", "Requirement already satisfied: nvidia-cusolver-cu12==******** in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (********)\n", "Requirement already satisfied: nvidia-cusparse-cu12==********** in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (**********)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (0.6.2)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (12.4.127)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (12.4.127)\n", "Requirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (3.2.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->torchmetrics) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch>=2.0.0->torchmetrics) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch>=2.0.0->torchmetrics) (3.0.2)\n", "Downloading torchmetrics-1.7.2-py3-none-any.whl (962 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m962.5/962.5 kB\u001b[0m \u001b[31m15.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading lightning_utilities-0.14.3-py3-none-any.whl (28 kB)\n", "Installing collected packages: lightning-utilities, torchmetrics\n", "Successfully installed lightning-utilities-0.14.3 torchmetrics-1.7.2\n"]}]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 39207, "status": "ok", "timestamp": 1748851359524, "user": {"displayName": "D J", "userId": "18240670107069857561"}, "user_tz": -480}, "id": "G2NzqWprZw59", "outputId": "e193a21b-c1a2-42cd-925e-1e7179c51187"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n"]}], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "d-wbLVxxj2zv", "executionInfo": {"status": "ok", "timestamp": 1748851771765, "user_tz": -480, "elapsed": 19055, "user": {"displayName": "D J", "userId": "18240670107069857561"}}}, "outputs": [], "source": ["import os\n", "\n", "try:\n", "    import gdal\n", "except:\n", "    from osgeo import gdal\n", "import numpy as np\n", "\n", "\n", "# 读取tif数据集\n", "def readTif(fileName):\n", "    dataset = gdal.Open(fileName)\n", "    if dataset is None:\n", "        print(fileName + \"文件无法打开\")\n", "    return dataset\n", "\n", "\n", "# 保存tif文件函数（改进数据类型处理和添加压缩方式）\n", "def writeTiff(im_data, im_geotrans, im_proj, path, datatype, compress='LZW'):\n", "    # 根据输入数据的维度判断波段数\n", "    if len(im_data.shape) == 3:\n", "        im_bands, im_height, im_width = im_data.shape\n", "    elif len(im_data.shape) == 2:\n", "        im_data = np.array([im_data])\n", "        im_bands, im_height, im_width = im_data.shape\n", "\n", "    # 创建文件，使用压缩选项（LZW, DEFLATE等）\n", "    driver = gdal.Get<PERSON><PERSON><PERSON><PERSON><PERSON>(\"GTiff\")\n", "    options = [\"COMPRESS={}\".format(compress)]\n", "    dataset = driver.Create(path, int(im_width), int(im_height), int(im_bands), datatype, options=options)\n", "\n", "    if dataset is not None:\n", "        dataset.SetGeoTransform(im_geotrans)  # 写入仿射变换参数\n", "        dataset.SetProjection(im_proj)  # 写入投影\n", "\n", "    # 写入每个波段\n", "    for i in range(im_bands):\n", "        dataset.GetRasterBand(i + 1).WriteArray(im_data[i])\n", "    del dataset\n", "\n", "\n", "# 像素坐标和地理坐标仿射变换\n", "def CoordTransf(Xpixel, Ypixel, GeoTransform):\n", "    XGeo = GeoTransform[0] + GeoTransform[1] * Xpixel + Ypixel * GeoTransform[2]\n", "    YGeo = GeoTransform[3] + GeoTransform[4] * Xpixel + Ypixel * GeoTransform[5]\n", "    return <PERSON><PERSON><PERSON>, YGeo\n", "\n", "\n", "# 裁剪函数，改进数据类型处理\n", "def TifCrop(Tif<PERSON>ath, SavePath, CropSize, RepetitionRate):\n", "    if not os.path.exists(SavePath):\n", "        os.makedirs(SavePath)\n", "\n", "    dataset_img = readTif(TifPath)\n", "    width = dataset_img.RasterXSize\n", "    height = dataset_img.RasterYSize\n", "    proj = dataset_img.GetProjection()\n", "    geotrans = dataset_img.GetGeoTransform()\n", "    img = dataset_img.ReadAsArray(0, 0, width, height)  # 获取数据\n", "\n", "    # 获取数据类型，保持与原始影像一致\n", "    datatype = dataset_img.GetRasterBand(1).DataType\n", "\n", "    new_name = len(os.listdir(SavePath)) + 1\n", "\n", "    for i in range(int((height - CropSize * RepetitionRate) / (CropSize * (1 - RepetitionRate)))):\n", "        for j in range(int((width - CropSize * RepetitionRate) / (CropSize * (1 - RepetitionRate)))):\n", "            # 如果图像是单波段\n", "            if len(img.shape) == 2:\n", "                cropped = img[\n", "                          int(i * CropSize * (1 - RepetitionRate)): int(i * CropSize * (1 - RepetitionRate)) + CropSize,\n", "                          int(j * CropSize * (1 - RepetitionRate)): int(j * CropSize * (1 - RepetitionRate)) + CropSize]\n", "            # 如果图像是多波段\n", "            else:\n", "                cropped = img[:,\n", "                          int(i * CropSize * (1 - RepetitionRate)): int(i * CropSize * (1 - RepetitionRate)) + CropSize,\n", "                          int(j * CropSize * (1 - RepetitionRate)): int(j * CropSize * (1 - RepetitionRate)) + CropSize]\n", "\n", "            XGeo, YGeo = CoordTransf(int(j * CropSize * (1 - RepetitionRate)),\n", "                                     int(i * CropSize * (1 - RepetitionRate)),\n", "                                     geotrans)\n", "            crop_geotrans = (XGeo, geotrans[1], geotrans[2], YGeo, geotrans[4], geotrans[5])\n", "            writeTiff(cropped, crop_geotrans, proj, SavePath + \"/%d.tif\" % new_name, datatype)\n", "            new_name += 1\n", "\n", "    # 向前裁剪最后一列\n", "    for i in range(int((height - CropSize * RepetitionRate) / (CropSize * (1 - RepetitionRate)))):\n", "        if len(img.shape) == 2:\n", "            cropped = img[int(i * CropSize * (1 - RepetitionRate)): int(i * CropSize * (1 - RepetitionRate)) + CropSize,\n", "                      (width - CropSize): width]\n", "        else:\n", "            cropped = img[:,\n", "                      int(i * CropSize * (1 - RepetitionRate)): int(i * CropSize * (1 - RepetitionRate)) + CropSize,\n", "                      (width - CropSize): width]\n", "\n", "        XGeo, YGeo = CoordTransf(width - CropSize, int(i * CropSize * (1 - RepetitionRate)), geotrans)\n", "        crop_geotrans = (XGeo, geotrans[1], geotrans[2], YGeo, geotrans[4], geotrans[5])\n", "        writeTiff(cropped, crop_geotrans, proj, SavePath + \"/%d.tif\" % new_name, datatype)\n", "        new_name += 1\n", "\n", "    # 向前裁剪最后一行\n", "    for j in range(int((width - CropSize * RepetitionRate) / (CropSize * (1 - RepetitionRate)))):\n", "        if len(img.shape) == 2:\n", "            cropped = img[(height - CropSize): height,\n", "                      int(j * CropSize * (1 - RepetitionRate)): int(j * CropSize * (1 - RepetitionRate)) + CropSize]\n", "        else:\n", "            cropped = img[:,\n", "                      (height - CropSize): height,\n", "                      int(j * CropSize * (1 - RepetitionRate)): int(j * CropSize * (1 - RepetitionRate)) + CropSize]\n", "\n", "        XGeo, YGeo = CoordTransf(int(j * CropSize * (1 - RepetitionRate)), height - CropSize, geotrans)\n", "        crop_geotrans = (XGeo, geotrans[1], geotrans[2], YGeo, geotrans[4], geotrans[5])\n", "        writeTiff(cropped, crop_geotrans, proj, SavePath + \"/%d.tif\" % new_name, datatype)\n", "        new_name += 1\n", "\n", "    # 裁剪右下角\n", "    if len(img.shape) == 2:\n", "        cropped = img[(height - CropSize): height, (width - CropSize): width]\n", "    else:\n", "        cropped = img[:, (height - CropSize): height, (width - CropSize): width]\n", "\n", "    XGeo, YGeo = CoordTransf(width - CropSize, height - CropSize, geotrans)\n", "    crop_geotrans = (XGeo, geotrans[1], geotrans[2], YGeo, geotrans[4], geotrans[5])\n", "    writeTiff(cropped, crop_geotrans, proj, SavePath + \"/%d.tif\" % new_name, datatype)\n", "    new_name += 1\n", "\n", "\n", "# 将影像1裁剪为重复率为0.1的64×64的数据集\n", "TifCrop(r\"/content/drive/MyDrive/d2l-zh/SRGAN/data/B0.tif\", r\"/content/drive/MyDrive/d2l-zh/SRGAN/data/train/NTL/\", 80, 0.5)\n"]}, {"cell_type": "code", "source": [], "metadata": {"id": "1tpzUV3eYIQx"}, "execution_count": null, "outputs": []}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}